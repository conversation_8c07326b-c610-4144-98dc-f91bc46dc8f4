# 德佟P2打印机对接说明

## 项目概述

本项目已成功完成德佟P2打印机的SDK对接，替换了原有的SUPVANAPIT50PRO SDK，实现了完整的标签打印功能。

## 主要变更

### 1. SDK适配层创建
- **位置**: `adapters/` 目录
- **文件**:
  - `BLETool.js` - 蓝牙设备管理适配层
  - `BLEToothManage.js` - 打印和预览功能适配层
  - `Constants.js` - 错误码和常量定义适配层

### 2. 配置文件更新

#### printerStatus.js
- 添加德佟P2专用错误码 (200-220)
- 基于德佟SDK LPA_Result错误码体系
- 新增错误码映射函数 `mapDetongErrorCode()`
- 用户友好的错误提示信息

#### templates.js
- 适配德佟P2支持的标签尺寸：40x30mm, 50x30mm, 60x40mm
- 自动间隙适配 (`Gap: 0`, `AutoGap: true`)
- 新增德佟P2兼容性检查函数
- 模板自动适配功能

### 3. 页面逻辑优化

#### printer.js
- 替换SDK引用为适配层
- 实现德佟P2默认兼容策略
- 耗材兼容性检查优化（默认兼容）
- 自动间隙适配逻辑

#### index页面
- 添加德佟P2 Demo入口按钮
- 美化的导航界面
- 用户友好的操作指引

## 德佟P2特性

### 自动适配功能
- ✅ **自动间隙检测**: 不需要手动设置Gap参数
- ✅ **默认兼容策略**: 耗材规格检查默认兼容
- ✅ **尺寸自适应**: 支持多种标签尺寸自动适配

### 支持的标签尺寸
| 尺寸 | 宽度(mm) | 高度(mm) | 说明 |
|------|----------|----------|------|
| 40x30 | 40 | 30 | 标准小标签 |
| 50x30 | 50 | 30 | 中等标签 |
| 60x40 | 60 | 40 | 大标签 |

### 错误码映射
| 德佟SDK错误码 | 标准错误码 | 说明 |
|---------------|------------|------|
| 0x00 | 0 | 成功 |
| 0x01 | 200 | 参数错误 |
| 0x02 | 201 | 未检测到打印机 |
| 0x03 | 202 | 打印机未连接 |
| 0x04 | 203 | 连接失败 |
| ... | ... | ... |

## 使用方法

### 1. 启动应用
```bash
# 在微信开发者工具中打开项目
# 确保已配置德佟P2打印机权限
```

### 2. 进入Demo
1. 打开小程序首页
2. 点击"进入标签打印Demo"按钮
3. 进入printer页面开始使用

### 3. 连接打印机
1. 确保德佟P2打印机已开启
2. 点击"连接打印机"按钮
3. 系统自动搜索并连接设备

### 4. 打印标签
1. 选择合适的模板尺寸
2. 填写标签内容（品名、操作人、日期）
3. 设置打印份数
4. 点击"打印"按钮

## 技术特点

### 无缝切换
- 保持原有API接口不变
- 通过适配层实现SDK替换
- 业务逻辑无需大幅修改

### 智能适配
- 自动检测德佟P2特性
- 智能错误码转换
- 用户友好的提示信息

### 兼容性优化
- 德佟P2默认兼容所有模板
- 自动间隙适配，无需手动设置
- 支持多种标签尺寸

## 注意事项

### 设备要求
- 德佟P2系列标签打印机
- 微信小程序环境
- 蓝牙权限已授权

### 功能限制
- 不支持获取实际耗材信息（使用默认兼容策略）
- 不支持手动设置间隙（自动适配）
- 部分高级功能可能需要德佟SDK更新

### 调试建议
- 开启德佟SDK日志 (`logLevel: 2`)
- 检查蓝牙权限配置
- 确认打印机型号兼容性

## 文件结构

```
welshine.printer.detong/
├── adapters/                 # 德佟SDK适配层
│   ├── BLETool.js            # 蓝牙设备管理
│   ├── BLEToothManage.js     # 打印功能管理
│   └── Constants.js          # 常量和错误码
├── config/                   # 配置文件
│   ├── printerStatus.js      # 状态码配置（已更新）
│   └── templates.js          # 模板配置（已适配）
├── pages/
│   ├── index/                # 首页（已添加Demo入口）
│   └── printer/              # 打印页面（已适配德佟P2）
├── js_sdk/                   # 德佟SDK
│   └── lpapi-ble/
└── test/                     # 测试文件
    └── adapter-test.js       # 适配层测试
```

## 更新日志

### v1.0.0 (2025-01-14)
- ✅ 完成德佟P2 SDK对接
- ✅ 创建完整适配层
- ✅ 更新配置文件
- ✅ 优化用户界面
- ✅ 实现自动兼容策略
- ✅ 添加Demo入口

## 联系支持

如有问题或需要技术支持，请联系开发团队。
