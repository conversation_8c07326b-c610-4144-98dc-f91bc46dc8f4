/**index.wxss**/
page,
.app {
    width: 100%;
    height: 100%;
}

.app {
    display: flex;
    flex-direction: column;
}

.app .app-header {
    height: auto;
}

.app .app-main {
    flex: 1;
}

.app .app-footer {
    height: auto;
}

.btn {
    margin: 0.5em 1em;
}

.section-group {
    margin: 1em;
}

.picker {
    padding: 5px 13px;
    background-color: #FFFFFF;
}

.print-canvas {
    position: fixed;
    left: -999999rpx;
    top: -999999rpx;
}

.preview-image {
    border: lightgray;
}

/* 德佟P2 Demo样式 */
.demo-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    margin: 1em;
}

.demo-desc {
    display: block;
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-top: 10px;
    padding: 0 20px;
}