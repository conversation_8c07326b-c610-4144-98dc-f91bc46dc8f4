/**
 * 德佟SDK BLETool适配层
 * 适配原有SUPVANAPIT50PRO BLETool接口到德佟SDK
 */

import { LPAPIFactory, LPAPI } from "../js_sdk/lpapi-ble/index";

class BLETool {
  constructor() {
    this.lpapi = null;
    this.isInitialized = false;
    this.currentScanCallback = null;
    this.connectedDevice = null;
  }

  /**
   * 初始化德佟SDK
   */
  async initLPAPI() {
    if (this.isInitialized && this.lpapi) {
      return this.lpapi;
    }

    try {
      // 德佟SDK可能不需要预先指定Canvas，在使用时再获取
      // 初始化LPAPI
      this.lpapi = LPAPIFactory.getInstance({
        logLevel: 2 // 适中的日志级别
      });

      this.isInitialized = true;
      console.log('德佟SDK初始化成功');
      return this.lpapi;
    } catch (error) {
      console.error('德佟SDK初始化失败:', error);
      // 如果初始化失败，尝试简单的初始化
      try {
        this.lpapi = LPAPIFactory.getInstance({});
        this.isInitialized = true;
        console.log('德佟SDK简单初始化成功');
        return this.lpapi;
      } catch (fallbackError) {
        console.error('德佟SDK简单初始化也失败:', fallbackError);
        throw fallbackError;
      }
    }
  }

  /**
   * 扫描蓝牙设备列表
   * @param {Function} callback 设备发现回调函数
   * @returns {Promise}
   */
  async scanBleDeviceList(callback) {
    try {
      await this.initLPAPI();
      
      this.currentScanCallback = callback;

      return new Promise((resolve, reject) => {
        this.lpapi.startBleDiscovery({
          timeout: 0, // 持续扫描
          deviceFound: (devices) => {
            // 适配回调格式
            if (devices && devices.length > 0) {
              const device = devices[0];
              const adaptedResponse = {
                ResultCode: 0,
                ResultValue: {
                  devices: [device]
                }
              };
              
              if (callback) {
                callback(adaptedResponse);
              }
            }
          },
          success: () => {
            console.log('开始扫描蓝牙设备');
            resolve({ ResultCode: 0, message: '扫描开始' });
          },
          fail: (error) => {
            console.error('扫描失败:', error);
            reject({ ResultCode: 103, message: '开始搜寻附近的蓝牙外围设备异常', error });
          }
        });
      });
    } catch (error) {
      console.error('扫描蓝牙设备异常:', error);
      throw { ResultCode: 103, message: '开始搜寻附近的蓝牙外围设备异常', error };
    }
  }

  /**
   * 停止扫描蓝牙设备
   * @returns {Promise}
   */
  async stopScanBleDevices() {
    try {
      if (this.lpapi) {
        await this.lpapi.stopBleDiscovery();
        console.log('停止扫描蓝牙设备');
        return { ResultCode: 117, message: '停止搜索蓝牙设备成功' };
      }
      return { ResultCode: 117, message: '停止搜索蓝牙设备成功' };
    } catch (error) {
      console.error('停止扫描失败:', error);
      throw { ResultCode: 106, message: '断开蓝牙失败异常', error };
    }
  }

  /**
   * 连接蓝牙设备
   * @param {Object} device 设备对象
   * @returns {Promise}
   */
  async connectBleDevice(device) {
    try {
      await this.initLPAPI();

      return new Promise((resolve, reject) => {
        this.lpapi.openPrinter({
          deviceId: device.deviceId || device.name,
          success: (result) => {
            console.log('连接设备成功:', result);
            this.connectedDevice = device;
            resolve({ ResultCode: 0, message: '连接成功', device });
          },
          fail: (error) => {
            console.error('连接设备失败:', error);
            reject({ ResultCode: 109, message: '连接蓝牙异常', error });
          }
        });
      });
    } catch (error) {
      console.error('连接蓝牙设备异常:', error);
      throw { ResultCode: 109, message: '连接蓝牙异常', error };
    }
  }

  /**
   * 断开蓝牙设备连接
   * @returns {Promise}
   */
  async disconnectBleDevice() {
    try {
      if (this.lpapi) {
        await this.lpapi.closePrinter();
        this.connectedDevice = null;
        console.log('断开蓝牙连接');
        return { ResultCode: 0, message: '断开连接成功' };
      }
      return { ResultCode: 0, message: '断开连接成功' };
    } catch (error) {
      console.error('断开连接失败:', error);
      throw { ResultCode: 106, message: '断开蓝牙失败异常', error };
    }
  }

  /**
   * 停止打印
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async stopPrint(callback) {
    try {
      if (this.lpapi) {
        // 德佟SDK可能没有直接的停止打印方法，这里模拟实现
        console.log('停止打印');
        const result = { ResultCode: 0, message: '停止打印成功' };
        if (callback) {
          callback(result);
        }
        return result;
      }
      const result = { ResultCode: 0, message: '停止打印成功' };
      if (callback) {
        callback(result);
      }
      return result;
    } catch (error) {
      console.error('停止打印失败:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) {
        callback(errorResult);
      }
      throw errorResult;
    }
  }

  /**
   * 获取当前连接的设备
   */
  getConnectedDevice() {
    return this.connectedDevice;
  }

  /**
   * 检查是否已连接设备
   */
  isConnected() {
    return !!this.connectedDevice;
  }
}

// 创建单例实例
const bleTool = new BLETool();

export default bleTool;
