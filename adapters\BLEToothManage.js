/**
 * 德佟SDK BLEToothManage适配层
 * 适配原有SUPVANAPIT50PRO BLEToothManage接口到德佟SDK
 */

import bleTool from './BLETool.js';

class BLEToothManage {
  constructor() {
    this.lpapi = null;
  }

  /**
   * 获取LPAPI实例
   */
  async getLPAPI() {
    if (!this.lpapi) {
      this.lpapi = await bleTool.initLPAPI();
    }
    return this.lpapi;
  }

  /**
   * 获取耗材信息
   * 注意：德佟P2打印机不支持获取耗材信息，这里返回默认兼容信息
   * @returns {Promise}
   */
  async ConsumableInformation() {
    try {
      console.log('德佟P2打印机不支持获取耗材信息，返回默认兼容信息');
      
      // 返回默认的兼容耗材信息
      const defaultMaterialInfo = {
        width: 40,  // 默认宽度40mm
        height: 30, // 默认高度30mm
        gap: 2,     // 默认间隙2mm
        type: 'label', // 标签类型
        compatible: true // 标记为兼容
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultMaterialInfo,
        message: '获取耗材信息成功（默认兼容）'
      });
    } catch (error) {
      console.error('获取耗材信息异常:', error);
      return Promise.reject({
        ResultCode: 129,
        message: '未检测到耗材',
        error
      });
    }
  }

  /**
   * 绘制预览图
   * @param {Object} canvas Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doDrawPreview(canvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();
      
      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];
      
      // 使用德佟SDK绘制预览
      const result = await lpapi.drawPreview({
        template: template,
        canvas: canvas,
        success: (res) => {
          console.log('预览绘制成功:', res);
          const successResult = { ResultCode: 0, message: '预览生成成功', data: res };
          if (callback) callback(successResult);
          return successResult;
        },
        fail: (error) => {
          console.error('预览绘制失败:', error);
          const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
          if (callback) callback(errorResult);
          throw errorResult;
        }
      });

      return result;
    } catch (error) {
      console.error('绘制预览异常:', error);
      const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 执行打印
   * @param {Object} textCanvas 文本Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doPrintMatrix(textCanvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();
      
      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      if (!textCanvas) {
        const error = { ResultCode: 108, message: '文本Canvas不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];
      
      // 检查是否已连接设备
      if (!bleTool.isConnected()) {
        const error = { ResultCode: 109, message: '设备未连接' };
        if (callback) callback(error);
        throw error;
      }

      console.log('开始打印，模板:', template);

      // 使用德佟SDK执行打印
      return new Promise((resolve, reject) => {
        lpapi.print({
          template: template,
          canvas: textCanvas,
          copies: template.Copies || 1,
          success: (result) => {
            console.log('打印成功:', result);
            const successResult = { ResultCode: 0, message: '打印成功', data: result };
            if (callback) callback(successResult);
            resolve(successResult);
          },
          fail: (error) => {
            console.error('打印失败:', error);
            
            // 根据错误类型返回相应的错误码
            let resultCode = 132; // 默认打印异常终止
            let message = '打印异常终止';
            
            if (error.message && error.message.includes('耗材')) {
              if (error.message.includes('仓盖')) {
                resultCode = 126;
                message = '请关闭耗材仓盖';
              } else if (error.message.includes('未装好')) {
                resultCode = 127;
                message = '耗材未装好';
              } else if (error.message.includes('余量')) {
                resultCode = 128;
                message = '请检查耗材余量';
              } else if (error.message.includes('未检测')) {
                resultCode = 129;
                message = '未检测到耗材';
              } else if (error.message.includes('未识别')) {
                resultCode = 130;
                message = '未识别到耗材';
              } else if (error.message.includes('用完')) {
                resultCode = 131;
                message = '耗材已用完';
              }
            } else if (error.message && error.message.includes('色带')) {
              resultCode = 133;
              message = '色带错误';
            }
            
            const errorResult = { ResultCode: resultCode, message, error };
            if (callback) callback(errorResult);
            reject(errorResult);
          }
        });
      });
    } catch (error) {
      console.error('打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 获取页面信息（德佟P2不支持，返回默认信息）
   * @returns {Promise}
   */
  async getPageInfos() {
    try {
      console.log('德佟P2打印机不支持获取页面信息，返回默认信息');
      
      const defaultPageInfo = {
        width: 40,
        height: 30,
        gap: 2,
        supported: false // 标记为不支持
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultPageInfo,
        message: '获取页面信息成功（默认信息）'
      });
    } catch (error) {
      console.error('获取页面信息异常:', error);
      return Promise.reject({
        ResultCode: 124,
        message: '生成图片数据异常',
        error
      });
    }
  }
}

// 创建单例实例
const bleToothManage = new BLEToothManage();

export default bleToothManage;
