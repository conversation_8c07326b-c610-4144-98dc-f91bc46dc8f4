/**
 * 德佟SDK BLEToothManage适配层
 * 适配原有SUPVANAPIT50PRO BLEToothManage接口到德佟SDK
 */

import bleTool from './BLETool.js';

class BLEToothManage {
  constructor() {
    this.lpapi = null;
  }

  /**
   * 获取LPAPI实例
   */
  async getLPAPI() {
    if (!this.lpapi) {
      this.lpapi = await bleTool.initLPAPI();
    }
    return this.lpapi;
  }

  /**
   * 获取耗材信息
   * 注意：德佟P2打印机不支持获取耗材信息，这里返回默认兼容信息
   * @returns {Promise}
   */
  async ConsumableInformation() {
    try {
      console.log('德佟P2打印机不支持获取耗材信息，返回默认兼容信息');
      
      // 返回默认的兼容耗材信息
      const defaultMaterialInfo = {
        width: 40,  // 默认宽度40mm
        height: 30, // 默认高度30mm
        gap: 2,     // 默认间隙2mm
        type: 'label', // 标签类型
        compatible: true // 标记为兼容
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultMaterialInfo,
        message: '获取耗材信息成功（默认兼容）'
      });
    } catch (error) {
      console.error('获取耗材信息异常:', error);
      return Promise.reject({
        ResultCode: 129,
        message: '未检测到耗材',
        error
      });
    }
  }

  /**
   * 绘制预览图
   * @param {Object} canvas Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doDrawPreview(canvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();
      
      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];
      
      // 德佟SDK预览绘制（简化实现）
      console.log('德佟SDK绘制预览，模板:', template);

      return new Promise((resolve, reject) => {
        try {
          // 创建预览任务 - 使用德佟SDK规定的预览jobName格式
          const jobResult = lpapi.startJob({
            width: template.Width || 40,
            height: template.Height || 30,
            orientation: template.Rotate || 0,
            jobName: "#!#preview#!#"  // 德佟SDK预览模式专用jobName
          });

          if (!jobResult) {
            throw new Error('预览任务创建失败');
          }

          // 绘制模板内容 - 参考index页面textPrintTest2的参数
          if (template.DrawObjects && Array.isArray(template.DrawObjects)) {
            template.DrawObjects.forEach(item => {
              if (item.Format === 'TEXT' || !item.Format) {
                // 确保FontSize是数字格式
                const fontSize = typeof item.FontSize === 'string' ?
                  parseFloat(item.FontSize) : (item.FontSize || 4);

                lpapi.drawText({
                  text: item.Content || '',
                  x: item.X || 0,
                  y: item.Y || 0,
                  width: item.Width || 10,
                  // height: item.Height,  // 不设置height，让SDK自动计算
                  fontHeight: fontSize,
                  fontName: item.FontName || '',
                  fontStyle: item.FontStyle || 0,
                  // 关键参数 - 参考index页面
                  autoReturn: 1,
                  lineSpace: 0,
                  charSpace: 0
                });
              }
            });
          }

          // 提交预览任务 - 参考index页面的参数
          lpapi.commitJob({
            gapType: 2, // 间隙纸
            darkness: template.Density || 2,
            printSpeed: template.Speed || 25
          }).then((resp) => {
            console.log('德佟SDK预览成功:', resp);
            if (resp.statusCode === 0) {
              const successResult = {
                ResultCode: 0,
                message: '预览生成成功',
                data: resp,
                previewData: resp.dataUrls || []
              };
              if (callback) callback(successResult);
              resolve(successResult);
            } else {
              throw new Error(`预览失败，statusCode: ${resp.statusCode}`);
            }
          }).catch(error => {
            console.error('德佟SDK预览提交失败:', error);
            const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
            if (callback) callback(errorResult);
            reject(errorResult);
          });

        } catch (error) {
          console.error('德佟SDK预览绘制失败:', error);
          const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
          if (callback) callback(errorResult);
          reject(errorResult);
        }
      });
    } catch (error) {
      console.error('绘制预览异常:', error);
      const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 执行打印
   * @param {Object} textCanvas 文本Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doPrintMatrix(textCanvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();

      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];

      // 检查是否已连接设备
      if (!bleTool.isConnected()) {
        const error = { ResultCode: 109, message: '设备未连接' };
        if (callback) callback(error);
        throw error;
      }

      console.log('德佟P2开始打印，模板:', template);

      // 方法1：先生成预览图，然后使用printImage打印
      return this.printWithPreviewImage(template, callback);
    } catch (error) {
      console.error('打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 通过预览图进行打印
   * @param {Object} template 模板对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async printWithPreviewImage(template, callback) {
    try {
      const lpapi = await this.getLPAPI();

      console.log('德佟P2：先生成预览图，再打印');

      return new Promise((resolve, reject) => {
        try {
          // 第一步：创建预览任务 - 先生成图片
          const jobResult = lpapi.startJob({
            width: template.Width || 40,
            height: template.Height || 30,
            orientation: template.Rotate || 0,
            jobName: "#!#preview#!#"  // 先用预览模式生成图片
          });

          if (!jobResult) {
            throw new Error('打印任务创建失败');
          }

          // 第二步：绘制模板内容 - 参考index页面textPrintTest2的参数
          if (template.DrawObjects && Array.isArray(template.DrawObjects)) {
            template.DrawObjects.forEach(item => {
              if (item.Format === 'TEXT' || !item.Format) {
                // 确保FontSize是数字格式
                const fontSize = typeof item.FontSize === 'string' ?
                  parseFloat(item.FontSize) : (item.FontSize || 4);

                lpapi.drawText({
                  text: item.Content || '',
                  x: item.X || 0,
                  y: item.Y || 0,
                  width: item.Width || 10,
                  // height: item.Height,  // 不设置height，让SDK自动计算
                  fontHeight: fontSize,
                  fontName: item.FontName || '',
                  fontStyle: item.FontStyle || 0,
                  // 关键参数 - 参考index页面
                  autoReturn: 1,
                  lineSpace: 0,
                  charSpace: 0
                });
              }
            });
          }

          // 第三步：提交任务生成图片 - 参考index页面的参数
          lpapi.commitJob({
            gapType: 2, // 间隙纸
            darkness: template.Density || 2,
            printSpeed: template.Speed || 25
          }).then((resp) => {
            console.log('德佟P2图片生成成功:', resp);

            if (resp.statusCode === 0 && resp.dataUrls && resp.dataUrls.length > 0) {
              // 第四步：使用printImage打印生成的图片
              const imageUrl = resp.dataUrls[0];

              lpapi.printImage({
                imageUrl: imageUrl,
                copies: template.Copies || 1,
                gapType: 2,
                darkness: template.Density || 2,
                printSpeed: template.Speed || 25
              }).then((printResp) => {
                console.log('德佟P2打印成功:', printResp);

                if (printResp.statusCode === 0) {
                  const successResult = {
                    ResultCode: 0,
                    message: '打印成功',
                    data: printResp
                  };
                  if (callback) callback(successResult);
                  resolve(successResult);
                } else {
                  throw new Error(`打印失败，statusCode: ${printResp.statusCode}`);
                }
              }).catch(printError => {
                console.error('德佟P2打印失败:', printError);
                this.handlePrintError(printError, callback, reject);
              });

            } else {
              throw new Error(`图片生成失败，statusCode: ${resp.statusCode}`);
            }
          }).catch(error => {
            console.error('德佟P2图片生成失败:', error);
            const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
            if (callback) callback(errorResult);
            reject(errorResult);
          });

        } catch (error) {
          console.error('德佟P2打印任务异常:', error);
          const errorResult = { ResultCode: 132, message: '打印异常终止', error };
          if (callback) callback(errorResult);
          reject(errorResult);
        }
      });
    } catch (error) {
      console.error('德佟P2打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 处理打印错误
   * @param {Object} error 错误对象
   * @param {Function} callback 回调函数
   * @param {Function} reject Promise reject函数
   */
  handlePrintError(error, callback, reject) {
    // 根据错误类型返回相应的错误码
    let resultCode = 132; // 默认打印异常终止
    let message = '打印异常终止';

    if (error.message && error.message.includes('耗材')) {
      if (error.message.includes('仓盖')) {
        resultCode = 126;
        message = '请关闭耗材仓盖';
      } else if (error.message.includes('未装好')) {
        resultCode = 127;
        message = '耗材未装好';
      } else if (error.message.includes('余量')) {
        resultCode = 128;
        message = '请检查耗材余量';
      } else if (error.message.includes('未检测')) {
        resultCode = 129;
        message = '未检测到耗材';
      } else if (error.message.includes('未识别')) {
        resultCode = 130;
        message = '未识别到耗材';
      } else if (error.message.includes('用完')) {
        resultCode = 131;
        message = '耗材已用完';
      }
    } else if (error.message && error.message.includes('色带')) {
      resultCode = 133;
      message = '色带错误';
    }

    const errorResult = { ResultCode: resultCode, message, error };
    if (callback) callback(errorResult);
    reject(errorResult);
  }

  /**
   * 获取页面信息（德佟P2不支持，返回默认信息）
   * @returns {Promise}
   */
  async getPageInfos() {
    try {
      console.log('德佟P2打印机不支持获取页面信息，返回默认信息');
      
      const defaultPageInfo = {
        width: 40,
        height: 30,
        gap: 2,
        supported: false // 标记为不支持
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultPageInfo,
        message: '获取页面信息成功（默认信息）'
      });
    } catch (error) {
      console.error('获取页面信息异常:', error);
      return Promise.reject({
        ResultCode: 124,
        message: '生成图片数据异常',
        error
      });
    }
  }
}

// 创建单例实例
const bleToothManage = new BLEToothManage();

export default bleToothManage;
