<!--index.wxml-->
<view class="app" style="flex-direction: column;">
    <!-- header -->
    <view class="page-section">
        <!-- 创建一个隐藏的画布，用于绘制打印内容 -->
        <canvas class="print-canvas" type="2d" id="{{canvasId}}"></canvas>
        <!-- 打印机列表 -->
        <picker mode="selector" range="{{deviceList}}" value="{{deviceIndex}}" range-key="name" bindchange="onDeviceChanged">
            <view class="picker">打印机：{{deviceList[deviceIndex].name}}</view>
        </picker>
        <!-- 出纸方向 -->
        <picker mode="selector" range="{{orientationList}}" value="{{orientationIndex}}" range-key="name" bindchange="onOrientationChanged">
            <view class="picker">出纸方向：{{orientationList[orientationIndex].name}}</view>
        </picker>
        <!-- 纸张类型 -->
        <picker mode="selector" range="{{gapList}}" value="{{gapIndex}}" range-key="name" bindchange="onGapTypeChanged">
            <view class="picker">纸张类型：{{gapList[gapIndex].name}}</view>
        </picker>
        <!-- 打印浓度 -->
        <picker mode="selector" range="{{darknessList}}" value="{{darknessIndex}}" range-key="name" bindchange="onDarknessChanged">
            <view class="picker">打印浓度：{{darknessList[darknessIndex].name}}</view>
        </picker>
        <!-- 打印速度 -->
        <picker mode="selector" range="{{speedList}}" value="{{speedIndex}}" range-key="name" bindchange="onSpeedChanged">
            <view class="picker">打印速度：{{speedList[speedIndex].name}}</view>
        </picker>
        <!-- 预览或打印 -->
        <view class="weui-cells weui-cells_after-title">
            <view class="weui-cell">
                <view class="weui-cell__bd">预览模式</view>
                <view class="weui-cell_tf">
                    <radio-group bindchange="onPrintModeChanged" style="margin-right: 10px;">
                        <label wx:for-items="{{printModes}}" wx:key="value" style="margin: 0 10px;">
                            <radio value="{{item.value}}" checked="{{item.checked}}"></radio>
                            <text>{{item.title}}</text>
                        </label>
                    </radio-group>
                </view>
            </view>
        </view>
    </view>
    <!-- 打印机相关 -->
    <view class="page-section">
        <view class="page-section-title">打印机操作</view>
        <view class="section-body">
            <button type="primary" class="btn" bindtap="startDiscovery">开始搜索打印机</button>
            <button type="primary" class="btn" bindtap="stopDiscovery">停止搜索打印机</button>
            <button type="primary" class="btn" bindtap="openPrinter">打开打印机</button>
            <button type="primary" class="btn" bindtap="closePrinter">关闭打印机</button>
        </view>
    </view>
    <!-- 绘制相关 -->
    <view class="page-section">
        <view class="page-section-title">绘制与打印</view>
        <view class="section-body">
            <button type="primary" plain="true" class="btn" bindtap="textPrintTest2">文本打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="qrcodePrintTest">QRCode二维码打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="pdf417PrintTest">PDF417二维码打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="dataMatrixPrintTest">DataMatrix二维码打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="barcodePrintTest">一维码打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="localImagePrintTest">本地图片打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="remoteImagePrintTest">网络图片打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="rectanglePrintTest">矢量图绘制打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="alignmentPrintTest">文本对齐打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="rotationPrintTest">文本旋转打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="tablePrintTest">表格打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="jsonPrintTest">JSON打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="dataBindPrintTest">JSON批量打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="wdfxPrintTest">WDFX文件打印测试</button>
            <button type="primary" plain="true" class="btn" bindtap="multiPagePrintTest">多页（循环）打印测试</button>
        </view>
    </view>
    <!-- 预览效果 -->
    <view class="page-section" style="text-align: center;padding: 10px;background-color: aliceblue;">
        <!-- <image src="{{previewImage}}" class="preview-image" mode="widthFix"></image> -->
        <image wx:for="{{previewList}}" wx:key="{{item.key}}" src="{{item.value}}" mode="widthFix"/>
    </view>
</view>